/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.metrics;

/** Collection of metric names. */
public class MetricNames {
    private MetricNames() {}

    public static final String SUFFIX_RATE = "PerSecond";

    public static final String IO_NUM_RECORDS_IN = "numRecordsIn";
    public static final String IO_NUM_RECORDS_OUT = "numRecordsOut";
    public static final String IO_NUM_RECORDS_IN_RATE = IO_NUM_RECORDS_IN + SUFFIX_RATE;
    public static final String IO_NUM_RECORDS_OUT_RATE = IO_NUM_RECORDS_OUT + SUFFIX_RATE;

    public static final String IO_NUM_BYTES_IN = "numBytesIn";
    public static final String IO_NUM_BYTES_OUT = "numBytesOut";
    public static final String IO_NUM_BYTES_IN_RATE = IO_NUM_BYTES_IN + SUFFIX_RATE;
    public static final String IO_NUM_BYTES_OUT_RATE = IO_NUM_BYTES_OUT + SUFFIX_RATE;

    public static final String IO_NUM_BUFFERS_IN = "numBuffersIn";
    public static final String IO_NUM_BUFFERS_OUT = "numBuffersOut";
    public static final String IO_NUM_BUFFERS_OUT_RATE = IO_NUM_BUFFERS_OUT + SUFFIX_RATE;

    public static final String IO_CURRENT_INPUT_WATERMARK = "currentInputWatermark";
    public static final String IO_CURRENT_INPUT_WATERMARK_PATERN = "currentInput%dWatermark";
    public static final String IO_CURRENT_OUTPUT_WATERMARK = "currentOutputWatermark";

    public static final String NUM_FIRED_TIMERS = "numFiredTimers";
    public static final String NUM_FIRED_TIMERS_RATE = "numFiredTimers" + SUFFIX_RATE;

    public static final String NUM_RUNNING_JOBS = "numRunningJobs";
    public static final String TASK_SLOTS_AVAILABLE = "taskSlotsAvailable";
    public static final String TASK_SLOTS_TOTAL = "taskSlotsTotal";
    public static final String NUM_REGISTERED_TASK_MANAGERS = "numRegisteredTaskManagers";
    public static final String NUM_PENDING_TASK_MANAGERS = "numPendingTaskManagers";

    public static final String NUM_RESTARTS = "numRestarts";
    public static final String NUM_RESCALES = "numRescales";

    public static final String MEMORY_USED = "Used";
    public static final String MEMORY_COMMITTED = "Committed";
    public static final String MEMORY_MAX = "Max";

    public static final String FILE_DESCRIPTOR_MAX = "Max";
    public static final String FILE_DESCRIPTOR_OPEN = "Open";

    public static final String IS_BACK_PRESSURED = "isBackPressured";

    public static final String CHECKPOINT_ALIGNMENT_TIME = "checkpointAlignmentTime";
    public static final String CHECKPOINT_START_DELAY_TIME = "checkpointStartDelayNanos";
    public static final String INITIALIZATION_TIME = "initializationTime";

    public static final String MAILBOX_START_DURATION = "MailboxStartDurationMs";
    public static final String READ_OUTPUT_DATA_DURATION = "ReadOutputDataDurationMs";
    public static final String INITIALIZE_STATE_DURATION = "InitializeStateDurationMs";
    public static final String GATE_RESTORE_DURATION = "GateRestoreDurationMs";
    public static final String DOWNLOAD_STATE_DURATION = "DownloadStateDurationMs";
    public static final String RESTORE_STATE_DURATION = "RestoreStateDurationMs";
    public static final String RESTORED_STATE_SIZE = "RestoredStateSizeBytes";
    public static final String RESTORE_ASYNC_COMPACTION_DURATION =
            "RestoreAsyncCompactionDurationMs";

    public static final String START_WORKER_FAILURE_RATE = "startWorkFailure" + SUFFIX_RATE;

    public static String currentInputWatermarkName(int index) {
        return String.format(IO_CURRENT_INPUT_WATERMARK_PATERN, index);
    }

    public static final String TASK_IDLE_TIME = "idleTimeMs" + SUFFIX_RATE;
    public static final String TASK_BUSY_TIME = "busyTimeMs" + SUFFIX_RATE;
    public static final String TASK_BACK_PRESSURED_TIME = "backPressuredTimeMs" + SUFFIX_RATE;
    public static final String ACC_TASK_IDLE_TIME = "accumulateIdleTimeMs";
    public static final String ACC_TASK_BUSY_TIME = "accumulateBusyTimeMs";
    public static final String ACC_TASK_BACK_PRESSURED_TIME = "accumulateBackPressuredTimeMs";
    public static final String TASK_SOFT_BACK_PRESSURED_TIME =
            "softBackPressuredTimeMs" + SUFFIX_RATE;
    public static final String TASK_HARD_BACK_PRESSURED_TIME =
            "hardBackPressuredTimeMs" + SUFFIX_RATE;
    public static final String CHANGELOG_BUSY_TIME = "changelogBusyTimeMs" + SUFFIX_RATE;
    public static final String TASK_MAX_SOFT_BACK_PRESSURED_TIME = "maxSoftBackPressureTimeMs";
    public static final String TASK_MAX_HARD_BACK_PRESSURED_TIME = "maxHardBackPressureTimeMs";

    public static final String ESTIMATED_TIME_TO_CONSUME_BUFFERS =
            "estimatedTimeToConsumeBuffersMs";
    public static final String DEBLOATED_BUFFER_SIZE = "debloatedBufferSize";

    // FLIP-33 sink
    public static final String NUM_RECORDS_OUT_ERRORS = "numRecordsOutErrors";
    public static final String NUM_RECORDS_SEND_ERRORS = "numRecordsSendErrors";
    public static final String CURRENT_SEND_TIME = "currentSendTime";
    public static final String NUM_RECORDS_SEND = "numRecordsSend";
    public static final String NUM_BYTES_SEND = "numBytesSend";

    // FLIP-33 source
    public static final String NUM_RECORDS_IN_ERRORS = "numRecordsInErrors";
    public static final String CURRENT_FETCH_EVENT_TIME_LAG = "currentFetchEventTimeLag";
    public static final String CURRENT_EMIT_EVENT_TIME_LAG = "currentEmitEventTimeLag";
    public static final String WATERMARK_LAG = "watermarkLag";
    public static final String PENDING_RECORDS = "pendingRecords";
    public static final String PENDING_BYTES = "pendingBytes";
    public static final String SOURCE_IDLE_TIME = "sourceIdleTime";

    // FLIP-182 (watermark alignment)
    public static final String WATERMARK_ALIGNMENT_DRIFT = "watermarkAlignmentDrift";

    public static final String MAILBOX_THROUGHPUT = "mailboxMailsPerSecond";
    public static final String MAILBOX_LATENCY = "mailboxLatencyMs";
    public static final String MAILBOX_SIZE = "mailboxQueueSize";

    // speculative execution
    public static final String NUM_SLOW_EXECUTION_VERTICES = "numSlowExecutionVertices";
    public static final String NUM_EFFECTIVE_SPECULATIVE_EXECUTIONS =
            "numEffectiveSpeculativeExecutions";

    // FLIP-221 for caches
    public static final String HIT_COUNT = "hitCount";
    public static final String MISS_COUNT = "missCount";
    public static final String LOAD_COUNT = "loadCount";
    public static final String NUM_LOAD_FAILURES = "numLoadFailures";
    public static final String LATEST_LOAD_TIME = "latestLoadTime";
    public static final String NUM_CACHED_RECORDS = "numCachedRecords";
    public static final String NUM_CACHED_BYTES = "numCachedBytes";

    // FLIP-27 for split enumerator
    public static final String UNASSIGNED_SPLITS = "unassignedSplits";

    // FLIP-371 for sink committer
    public static final String TOTAL_COMMITTABLES = "totalCommittables";
    public static final String SUCCESSFUL_COMMITTABLES = "successfulCommittables";
    public static final String ALREADY_COMMITTED_COMMITTABLES = "alreadyCommittedCommittables";
    public static final String FAILED_COMMITTABLES = "failedCommittables";
    public static final String RETRIED_COMMITTABLES = "retriedCommittables";
    public static final String PENDING_COMMITTABLES = "pendingCommittables";

    // FLIP-513 split level metrics
    public static final String SPLIT_CURRENT_WATERMARK = "currentWatermark";
    public static final String SPLIT_ACTIVE_TIME = "activeTimeMs" + SUFFIX_RATE;
    public static final String SPLIT_PAUSED_TIME = "pausedTimeMs" + SUFFIX_RATE;
    public static final String SPLIT_IDLE_TIME = "idleTimeMs" + SUFFIX_RATE;
    public static final String ACC_SPLIT_PAUSED_TIME = "accumulatedPausedTimeMs";
    public static final String ACC_SPLIT_ACTIVE_TIME = "accumulatedActiveTimeMs";
    public static final String ACC_SPLIT_IDLE_TIME = "accumulatedIdleTimeMs";
}
