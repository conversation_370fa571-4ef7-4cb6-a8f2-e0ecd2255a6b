/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.clusterframework.types;

import org.apache.flink.annotation.VisibleForTesting;
import org.apache.flink.runtime.taskmanager.TaskManagerLocation;

import java.util.Collection;
import java.util.Collections;

/** Testing utils for {@link SlotProfile}. */
public class SlotProfileTestingUtils {

    /** Returns a slot profile that has no requirements. */
    @VisibleForTesting
    public static SlotProfile noRequirements() {
        return noLocality(ResourceProfile.UNKNOWN);
    }

    /** Returns a slot profile for the given resource profile, without any locality requirements. */
    @VisibleForTesting
    public static SlotProfile noLocality(ResourceProfile resourceProfile) {
        return preferredLocality(resourceProfile, Collections.emptyList());
    }

    /**
     * Returns a slot profile for the given resource profile and the preferred locations.
     *
     * @param resourceProfile specifying the slot requirements
     * @param preferredLocations specifying the preferred locations
     * @return Slot profile with the given resource profile and preferred locations
     */
    @VisibleForTesting
    public static SlotProfile preferredLocality(
            final ResourceProfile resourceProfile,
            final Collection<TaskManagerLocation> preferredLocations) {

        return SlotProfile.priorAllocation(
                resourceProfile,
                resourceProfile,
                preferredLocations,
                Collections.emptyList(),
                Collections.emptySet());
    }
}
