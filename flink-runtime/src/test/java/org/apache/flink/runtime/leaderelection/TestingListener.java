/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.leaderelection;

import org.apache.flink.runtime.leaderretrieval.LeaderRetrievalListener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;

/**
 * Test {@link LeaderRetrievalListener} implementation which offers some convenience functions for
 * testing purposes.
 */
public class TestingListener extends TestingRetrievalBase implements LeaderRetrievalListener {

    private static final Logger LOG = LoggerFactory.getLogger(TestingListener.class);

    @Override
    public void notifyLeaderAddress(String leaderAddress, UUID leaderSessionID) {
        LOG.debug(
                "Notified about new leader address {} with session ID {}.",
                leaderAddress,
                leaderSessionID);
        if (leaderAddress == null && leaderSessionID == null) {
            offerToLeaderQueue(LeaderInformation.empty());
        } else {
            offerToLeaderQueue(LeaderInformation.known(leaderSessionID, leaderAddress));
        }
    }

    @Override
    public void handleError(Exception exception) {
        super.handleError(exception);
    }
}
