/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.scheduler;

import org.apache.flink.api.common.JobID;
import org.apache.flink.api.common.JobStatus;
import org.apache.flink.runtime.executiongraph.JobStatusListener;

import java.util.concurrent.CompletableFuture;

/** {@link JobStatusListener} which records a globally terminal {@link JobStatus}. */
public class GloballyTerminalJobStatusListener implements JobStatusListener {

    private final CompletableFuture<JobStatus> globallyTerminalJobStatusFuture =
            new CompletableFuture<>();

    @Override
    public void jobStatusChanges(JobID jobId, JobStatus newJobStatus, long timestamp) {
        if (newJobStatus.isGloballyTerminalState()) {
            globallyTerminalJobStatusFuture.complete(newJobStatus);
        }
    }

    public CompletableFuture<JobStatus> getTerminationFuture() {
        return globallyTerminalJobStatusFuture;
    }
}
