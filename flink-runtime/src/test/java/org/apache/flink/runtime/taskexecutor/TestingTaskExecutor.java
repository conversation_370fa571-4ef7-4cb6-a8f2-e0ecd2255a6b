/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.taskexecutor;

import org.apache.flink.runtime.blob.TaskExecutorBlobService;
import org.apache.flink.runtime.externalresource.ExternalResourceInfoProvider;
import org.apache.flink.runtime.heartbeat.HeartbeatServices;
import org.apache.flink.runtime.highavailability.HighAvailabilityServices;
import org.apache.flink.runtime.io.network.partition.TaskExecutorPartitionTracker;
import org.apache.flink.runtime.metrics.groups.TaskManagerMetricGroup;
import org.apache.flink.runtime.rpc.FatalErrorHandler;
import org.apache.flink.runtime.rpc.MainThreadExecutable;
import org.apache.flink.runtime.rpc.RpcService;
import org.apache.flink.runtime.security.token.DelegationTokenReceiverRepository;

import javax.annotation.Nullable;

import java.util.concurrent.CompletableFuture;

/** {@link TaskExecutor} extension for testing purposes. */
class TestingTaskExecutor extends TaskExecutor {
    private final CompletableFuture<Void> startFuture = new CompletableFuture<>();

    public TestingTaskExecutor(
            RpcService rpcService,
            TaskManagerConfiguration taskManagerConfiguration,
            HighAvailabilityServices haServices,
            TaskManagerServices taskExecutorServices,
            ExternalResourceInfoProvider externalResourceInfoProvider,
            HeartbeatServices heartbeatServices,
            TaskManagerMetricGroup taskManagerMetricGroup,
            @Nullable String metricQueryServiceAddress,
            TaskExecutorBlobService taskExecutorBlobService,
            FatalErrorHandler fatalErrorHandler,
            TaskExecutorPartitionTracker partitionTracker,
            DelegationTokenReceiverRepository delegationTokenReceiverRepository) {
        super(
                rpcService,
                taskManagerConfiguration,
                haServices,
                taskExecutorServices,
                externalResourceInfoProvider,
                heartbeatServices,
                taskManagerMetricGroup,
                metricQueryServiceAddress,
                taskExecutorBlobService,
                fatalErrorHandler,
                partitionTracker,
                delegationTokenReceiverRepository);
    }

    @Override
    public void onStart() throws Exception {
        try {
            super.onStart();
        } catch (Exception e) {
            startFuture.completeExceptionally(e);
            throw e;
        }

        startFuture.complete(null);
    }

    void waitUntilStarted() {
        startFuture.join();
    }

    MainThreadExecutable getMainThreadExecutableForTesting() {
        return this.rpcServer;
    }
}
