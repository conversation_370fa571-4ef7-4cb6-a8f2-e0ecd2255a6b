/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.taskexecutor.slot;

import org.apache.flink.runtime.clusterframework.types.AllocationID;

import javax.annotation.Nonnull;

import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/** {@link SlotActions} implementation for testing purposes. */
public class TestingSlotActions implements SlotActions {

    @Nonnull private final Consumer<AllocationID> freeSlotConsumer;

    @Nonnull private final BiConsumer<AllocationID, UUID> timeoutSlotConsumer;

    public TestingSlotActions(
            @Nonnull Consumer<AllocationID> freeSlotConsumer,
            @Nonnull BiConsumer<AllocationID, UUID> timeoutSlotConsumer) {
        this.freeSlotConsumer = freeSlotConsumer;
        this.timeoutSlotConsumer = timeoutSlotConsumer;
    }

    @Override
    public void freeSlot(AllocationID allocationId) {
        freeSlotConsumer.accept(allocationId);
    }

    @Override
    public void timeoutSlot(AllocationID allocationId, UUID ticket) {
        timeoutSlotConsumer.accept(allocationId, ticket);
    }
}
