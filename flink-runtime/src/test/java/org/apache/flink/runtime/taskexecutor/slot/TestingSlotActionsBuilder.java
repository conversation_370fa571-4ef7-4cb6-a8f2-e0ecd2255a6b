/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.runtime.taskexecutor.slot;

import org.apache.flink.runtime.clusterframework.types.AllocationID;

import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/** Builder for the {@link TestingSlotActions}. */
public class TestingSlotActionsBuilder {
    private Consumer<AllocationID> freeSlotConsumer = ignored -> {};
    private BiConsumer<AllocationID, UUID> timeoutSlotConsumer = (ignoredA, ignoredB) -> {};

    public TestingSlotActionsBuilder setFreeSlotConsumer(Consumer<AllocationID> freeSlotConsumer) {
        this.freeSlotConsumer = freeSlotConsumer;
        return this;
    }

    public TestingSlotActionsBuilder setTimeoutSlotConsumer(
            BiConsumer<AllocationID, UUID> timeoutSlotConsumer) {
        this.timeoutSlotConsumer = timeoutSlotConsumer;
        return this;
    }

    public TestingSlotActions build() {
        return new TestingSlotActions(freeSlotConsumer, timeoutSlotConsumer);
    }
}
