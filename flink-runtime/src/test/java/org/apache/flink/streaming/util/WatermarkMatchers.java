/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.streaming.util;

import org.apache.flink.streaming.api.watermark.Watermark;

import org.hamcrest.FeatureMatcher;
import org.hamcrest.Matcher;

import static org.hamcrest.CoreMatchers.equalTo;

/** Matchers for {@link org.apache.flink.streaming.api.watermark.Watermark}. */
public class WatermarkMatchers {

    /** Creates a matcher that matches when the examined watermark has the given timestamp. */
    public static Matcher<Watermark> legacyWatermark(long timestamp) {
        return new FeatureMatcher<Watermark, Long>(
                equalTo(timestamp), "a watermark with value", "value of watermark") {
            @Override
            protected Long featureValueOf(Watermark actual) {
                return actual.getTimestamp();
            }
        };
    }

    private WatermarkMatchers() {}
}
